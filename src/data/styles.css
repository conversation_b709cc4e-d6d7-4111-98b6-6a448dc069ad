:root {
    /* ------- Base Colors ------- */
    --primary-color: #27272a;
    --secondary-color: #65656d;
    --tertiary-color: #acacb4;
    --quaternary-color: #e4e4e7;
    --link-color: #14b8a6;
    --accent-color: #3b82f6;

    /* ------- Glassmorphism Colors ------- */
    --glass-base: rgba(197, 197, 197, 0.25);
    --glass-hover: rgba(197, 197, 197, 0.35);
    --glass-active: rgba(197, 197, 197, 0.45);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-border-hover: rgba(255, 255, 255, 0.3);

    /* ------- Progress Bar Colors ------- */
    --progress-primary: #3b82f6;
    --progress-secondary: #14b8a6;
    --progress-tertiary: #8b5cf6;
    --progress-glow: rgba(59, 130, 246, 0.4);
    --progress-glow-secondary: rgba(20, 184, 166, 0.3);

    /* ------- Interactive States ------- */
    --hover-overlay: rgba(255, 255, 255, 0.1);
    --active-overlay: rgba(255, 255, 255, 0.15);
    --focus-ring: rgba(59, 130, 246, 0.5);

    /* ------- Dark Mode Colors ------- */
    --dark-glass-base: rgba(159, 159, 159, 0.2);
    --dark-glass-hover: rgba(159, 159, 159, 0.3);
    --dark-glass-active: rgba(159, 159, 159, 0.4);
    --dark-glass-border: rgba(255, 255, 255, 0.1);
    --dark-glass-border-hover: rgba(255, 255, 255, 0.15);
    --dark-hover-overlay: rgba(255, 255, 255, 0.05);
    --dark-active-overlay: rgba(255, 255, 255, 0.1);
    /* ----------------------------------- */

    /* ------- fonts ------- */
    --primary-font: "Poppins", sans-serif;
    --secondary-font: "Montserrat", sans-serif;
    --heading-weight: 600;
    --body-weight: 400;
    --heading-line-height: 1.2;
    --body-line-height: 1.6;
    /* --------------------- */

    /* ------- spacing ------- */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    /* ----------------------- */

    /* ------- transitions ------- */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    /* --------------------------- */
}
