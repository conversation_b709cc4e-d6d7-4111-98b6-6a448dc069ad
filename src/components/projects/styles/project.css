@import "../../../data/styles.css";

.project {
    mix-blend-mode: normal;
    min-height: 300px; /* Increased minimum height */
    height: auto; /* Allow height to adjust to content */
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
    padding: 10px 0;
    margin: 0 auto;
}

.project a {
    text-decoration: none;
}

.project-container{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}



.all-project-container {
    height: 100%;
    padding: 2px;
}

.project-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 15px;
}

.project-logo {
    width: 50px;
    height: 50px;
    padding-right: 15px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.project-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.project-title {
    flex-grow: 1;
}

.project-title h3 {
    font-size: 1.5rem;
    margin: 0;
    color: var(--primary-color);
}

.project-content {
    width: 100%;
    overflow: hidden;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    padding: 0 5px;
}


.project-description {
    color: var(--secondary-color);
}

.project-link {
    display: flex;
    align-items: center;
    color: var(--secondary-color);
    font-size: 12px;
}

.project-link-icon {
    padding-left: 5px;
    font-size: 13px;
}

.project-link-text {
    padding-left: 20px;
    font-weight: 700;
}

.project-description-list {
    padding-left: 20px;
    margin-top: 12px;
    margin-bottom: 10px;
    list-style-type: disc;
    overflow: hidden;
    flex-grow: 1;
    font-size: 1rem;
    line-height: 1.5;
}

.project-description-list li {
    margin-bottom: 8px;
    line-height: 1.5;
    position: relative;
    padding-left: 5px;
    color: var(--secondary-color);
}

@media (max-width: 768px) {
    .project {
        min-height: 320px; /* Slightly taller on mobile */
        height: auto;
    }

    .project-description-list {
        font-size: 0.95rem;
    }
}

@media (max-width: 480px) {
    .project {
        min-height: 350px;
    }

    .project-logo {
        width: 40px;
        height: 40px;
    }

    .project-description-list {
        font-size: 0.9rem;
        margin-top: 8px;
    }
}
