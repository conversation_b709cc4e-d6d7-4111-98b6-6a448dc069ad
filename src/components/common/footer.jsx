import React from 'react';
import {motion} from 'motion/react';
import {FontAwesomeIcon} from '@fortawesome/react-fontawesome';
import {faHeart} from '@fortawesome/free-solid-svg-icons';
import './styles/footer.css';

const Footer = () => {
	return (
		<React.Fragment>
			<motion.div
				initial={{opacity: 0}}
				whileInView={{opacity: 1}}
				transition={{duration: 0.75}}
				className="footer bg-blur">
				<div className="footer-credits">
					<div className="footer-credits-text">
						Designed by <PERSON><PERSON>en © 2025
					</div>
				</div>
			</motion.div>
		</React.Fragment>
	);
};

export default Footer;
