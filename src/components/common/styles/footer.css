@import "../../../data/styles.css";

.footer {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 85%;
    max-width: 1200px;
    height: 80px;
    background: var(--glass-base);
    backdrop-filter: blur(20px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 4px 16px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--glass-border);
    position: relative;
    margin-top: 30px;
    border-radius: 25px;
    padding: 0 30px;
    transition: all var(--transition-normal);
    overflow: hidden;
}

.footer:hover {
    background: var(--glass-hover);
    border-color: var(--glass-border-hover);
    transform: translateY(-2px);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.15),
        0 6px 20px rgba(0, 0, 0, 0.08),
        0 0 30px rgba(255, 255, 255, 0.1);
}

.footer-credits {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    position: relative;
}

.footer-credits-text {
    color: var(--secondary-color);
    font-size: 0.95rem;
    text-align: center;
    font-weight: 500;
    letter-spacing: 0.3px;
    padding: 0 15px;
    transition: color var(--transition-fast);
    line-height: 1.4;
}

.footer:hover .footer-credits-text {
    color: var(--primary-color);
}

/* Dark mode styles */
.dark .footer {
    background: var(--dark-glass-base);
    border-color: var(--dark-glass-border);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.2),
        0 4px 16px rgba(0, 0, 0, 0.1);
}

.dark .footer:hover {
    background: var(--dark-glass-hover);
    border-color: var(--dark-glass-border-hover);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.3),
        0 6px 20px rgba(0, 0, 0, 0.15),
        0 0 25px rgba(255, 255, 255, 0.05);
}

.dark .footer-credits-text {
    color: var(--dark-secondary-text);
}

.dark .footer:hover .footer-credits-text {
    color: var(--dark-primary-text);
}

/* Subtle background pattern for enhanced depth */
.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 50%,
        rgba(255, 255, 255, 0.05) 100%
    );
    pointer-events: none;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.footer:hover::before {
    opacity: 1;
}

.dark .footer::before {
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0%,
        transparent 50%,
        rgba(255, 255, 255, 0.02) 100%
    );
}

/* Responsive Design */
@media (max-width: 1270px) {
    .footer {
        width: 90%;
        max-width: 1000px;
        height: 75px;
        padding: 0 25px;
        margin-top: 25px;
    }

    .footer-credits-text {
        font-size: 0.9rem;
    }
}

@media (max-width: 1024px) {
    .footer {
        width: 92%;
        max-width: 800px;
        height: 70px;
        padding: 0 20px;
        border-radius: 20px;
    }

    .footer-credits-text {
        font-size: 0.85rem;
        letter-spacing: 0.2px;
    }
}

@media (max-width: 768px) {
    .footer {
        width: 95%;
        height: 65px;
        padding: 0 18px;
        margin-top: 20px;
        border-radius: 18px;
    }

    .footer-credits-text {
        font-size: 0.8rem;
        padding: 0 12px;
    }

    .footer:hover {
        transform: translateY(-1px);
    }
}

@media (max-width: 480px) {
    .footer {
        width: 96%;
        height: 60px;
        padding: 0 15px;
        margin-top: 18px;
        border-radius: 15px;
    }

    .footer-credits-text {
        font-size: 0.75rem;
        padding: 0 10px;
        letter-spacing: 0.1px;
    }
}

@media (max-width: 380px) {
    .footer {
        width: 98%;
        height: 55px;
        padding: 0 12px;
        margin-top: 15px;
        border-radius: 12px;
    }

    .footer-credits-text {
        font-size: 0.7rem;
        padding: 0 8px;
        letter-spacing: 0px;
    }

    /* Reduce hover effects on very small screens */
    .footer:hover {
        transform: none;
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.1),
            0 4px 16px rgba(0, 0, 0, 0.05);
    }

    .dark .footer:hover {
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.2),
            0 4px 16px rgba(0, 0, 0, 0.1);
    }
}

/* Performance optimizations */
.footer {
    will-change: transform, background, border-color;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .footer,
    .footer:hover,
    .footer::before,
    .footer-credits-text {
        transition: none !important;
        animation: none !important;
        transform: none !important;
    }
}

/* Focus states for accessibility */
.footer:focus-within {
    outline: 2px solid var(--focus-ring);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .footer {
        border-width: 2px;
        background: rgba(255, 255, 255, 0.9);
    }

    .dark .footer {
        background: rgba(0, 0, 0, 0.9);
        border-color: rgba(255, 255, 255, 0.8);
    }

    .footer-credits-text {
        font-weight: 600;
    }
}

/* Print styles */
@media print {
    .footer {
        background: white !important;
        border: 1px solid #000 !important;
        box-shadow: none !important;
        backdrop-filter: none !important;
    }

    .footer-credits-text {
        color: #000 !important;
    }
}

