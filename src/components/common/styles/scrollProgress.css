.scroll-progress-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background-color: rgba(197, 197, 197, 0.2);
    backdrop-filter: blur(5px);
    z-index: 1000;
}

.scroll-progress-bar {
    height: 100%;
    background-color: rgba(197, 197, 197, 0.7);
    box-shadow: 0 1px 3px rgba(54, 54, 54, 0.2);
    width: 0;
    transition: width 0.1s ease;
}

.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background-color: rgba(197, 197, 197, 0.3);
    backdrop-filter: blur(10px);
    color: var(--primary-color);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 1000;
    border: none;
    box-shadow: 0 4px 8px rgba(54, 54, 54, 0.2);
    transition: all var(--transition-fast);
}

.back-to-top:hover {
    transform: translateY(-5px);
    background-color: rgba(197, 197, 197, 0.5);
    box-shadow: 0 6px 12px rgba(54, 54, 54, 0.3);
}

@media (max-width: 768px) {
    .back-to-top {
        width: 40px;
        height: 40px;
        bottom: 20px;
        right: 20px;
    }
}
