.scroll-progress-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--glass-base);
    backdrop-filter: blur(20px);
    z-index: 1002;
    border-bottom: 1px solid var(--glass-border);
    overflow: hidden;
    transition: all var(--transition-fast);
}

.scroll-progress-container.scrolling {
    height: 4px;
    background: var(--glass-hover);
}

.scroll-progress-bar {
    height: 100%;
    background: linear-gradient(90deg,
        var(--progress-primary) 0%,
        var(--progress-secondary) 20%,
        var(--progress-tertiary) 40%,
        var(--progress-quaternary) 60%,
        var(--progress-tertiary) 80%,
        var(--progress-primary) 100%);
    background-size: 300% 100%;
    width: 0;
    transition:
        width 0.15s cubic-bezier(0.4, 0, 0.2, 1),
        opacity 0.3s ease,
        box-shadow 0.3s ease;
    border-radius: 0 3px 3px 0;
    position: relative;
    opacity: 0;
    box-shadow:
        0 0 15px var(--progress-glow),
        0 0 30px var(--progress-glow-secondary);
}

.scroll-progress-bar.active {
    opacity: 1;
    animation: progressFlow 4s ease-in-out infinite;
    box-shadow:
        0 0 20px var(--progress-glow),
        0 0 40px var(--progress-glow-secondary),
        0 1px 3px rgba(0, 0, 0, 0.1);
}

.scroll-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.2) 50%,
        transparent 100%);
    animation: progressGlide 3s ease-in-out infinite;
    opacity: 0.7;
}

.scroll-progress-bar.active::after {
    opacity: 1;
}

/* Optimized animations for better performance */
@keyframes progressFlow {
    0% { background-position: 0% 0; }
    50% { background-position: 100% 0; }
    100% { background-position: 0% 0; }
}

@keyframes progressGlide {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    50% {
        transform: translateX(0%);
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Pause animations when user prefers reduced motion */
@media (prefers-reduced-motion: reduce) {
    .scroll-progress-bar,
    .scroll-progress-bar::after {
        animation: none;
    }

    .scroll-progress-container {
        transition: none;
    }
}

.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 52px;
    height: 52px;
    border-radius: 50%;
    background: var(--glass-base);
    backdrop-filter: blur(20px);
    color: var(--primary-color);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 1001;
    border: 1px solid var(--glass-border);
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04);
    transition:
        all var(--transition-normal),
        opacity 0.3s ease,
        transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 1.1rem;
    opacity: 0;
    transform: scale(0.8);
}

.back-to-top:hover {
    transform: translateY(-4px) scale(1.08);
    background: var(--glass-hover);
    box-shadow:
        0 8px 30px rgba(0, 0, 0, 0.12),
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 0 20px rgba(255, 255, 255, 0.4);
    color: var(--icon-hover);
    border-color: var(--glass-border-hover);
}

.back-to-top:active {
    transform: translateY(-2px) scale(1.02);
}

/* Dark mode styles */
.dark .scroll-progress-container {
    background: var(--dark-glass-base);
    border-bottom: 1px solid var(--dark-glass-border);
}

.dark .scroll-progress-container.scrolling {
    background: var(--dark-glass-hover);
}

.dark .scroll-progress-bar {
    box-shadow:
        0 0 25px var(--progress-glow),
        0 0 50px var(--progress-glow-secondary);
}

.dark .scroll-progress-bar.active {
    box-shadow:
        0 0 30px var(--progress-glow),
        0 0 60px var(--progress-glow-secondary),
        0 1px 3px rgba(0, 0, 0, 0.2);
}

.dark .back-to-top {
    background: var(--dark-glass-base);
    color: #e0e0e0;
    border: 1px solid var(--dark-glass-border);
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.15),
        0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .back-to-top:hover {
    background: var(--dark-glass-hover);
    color: var(--dark-icon-hover);
    border-color: var(--dark-glass-border-hover);
    box-shadow:
        0 8px 30px rgba(0, 0, 0, 0.2),
        0 4px 12px rgba(0, 0, 0, 0.15),
        0 0 15px rgba(255, 255, 255, 0.2);
}

/* Responsive Design */
@media (max-width: 1270px) {
    .scroll-progress-container {
        height: 3px;
    }

    .back-to-top {
        width: 48px;
        height: 48px;
        font-size: 1.05rem;
    }
}

@media (max-width: 1024px) {
    .scroll-progress-container {
        height: 2.5px;
    }

    .back-to-top {
        width: 46px;
        height: 46px;
        font-size: 1rem;
        bottom: 25px;
        right: 25px;
    }
}

@media (max-width: 768px) {
    .scroll-progress-container {
        height: 2px;
    }

    .scroll-progress-container.scrolling {
        height: 3px;
    }

    .back-to-top {
        width: 44px;
        height: 44px;
        bottom: 22px;
        right: 22px;
        font-size: 0.95rem;
    }
}

@media (max-width: 480px) {
    .scroll-progress-container {
        height: 2px;
    }

    .scroll-progress-bar {
        border-radius: 0 2px 2px 0;
    }

    .back-to-top {
        width: 42px;
        height: 42px;
        bottom: 20px;
        right: 20px;
        font-size: 0.9rem;
    }
}

@media (max-width: 380px) {
    .scroll-progress-container {
        height: 1.5px;
    }

    .scroll-progress-container.scrolling {
        height: 2px;
    }

    .back-to-top {
        width: 40px;
        height: 40px;
        bottom: 18px;
        right: 18px;
        font-size: 0.85rem;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .scroll-progress-bar {
        box-shadow:
            0 0 20px rgba(59, 130, 246, 0.35),
            0 0 40px rgba(20, 184, 166, 0.18);
    }

    .dark .scroll-progress-bar.active {
        box-shadow:
            0 0 25px rgba(59, 130, 246, 0.55),
            0 0 50px rgba(20, 184, 166, 0.28);
    }
}
