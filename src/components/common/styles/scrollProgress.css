.scroll-progress-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    z-index: 1002;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    overflow: hidden;
}

.scroll-progress-bar {
    height: 100%;
    background: linear-gradient(90deg,
        var(--accent-color) 0%,
        var(--link-color) 50%,
        var(--accent-color) 100%);
    background-size: 200% 100%;
    animation: progressShimmer 3s ease-in-out infinite;
    box-shadow:
        0 0 20px rgba(59, 130, 246, 0.4),
        0 0 40px rgba(20, 184, 166, 0.2);
    width: 0;
    transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0 4px 4px 0;
    position: relative;
}

.scroll-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 100%);
    animation: progressGlide 2s ease-in-out infinite;
}

@keyframes progressShimmer {
    0% { background-position: -200% 0; }
    50% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

@keyframes progressGlide {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(197, 197, 197, 0.25);
    backdrop-filter: blur(15px);
    color: var(--primary-color);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 1001;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all var(--transition-normal);
    font-size: 1.2rem;
}

.back-to-top:hover {
    transform: translateY(-3px) scale(1.05);
    background-color: rgba(197, 197, 197, 0.4);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    color: var(--accent-color);
}

/* Dark mode styles */
.dark .scroll-progress-container {
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.02);
}

.dark .scroll-progress-bar {
    box-shadow:
        0 0 25px rgba(59, 130, 246, 0.6),
        0 0 50px rgba(20, 184, 166, 0.3);
}

.dark .back-to-top {
    background-color: rgba(159, 159, 159, 0.24);
    color: #e0e0e0;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .back-to-top:hover {
    background-color: rgba(159, 159, 159, 0.4);
    color: var(--accent-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .back-to-top {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    .scroll-progress-container {
        height: 2px;
    }

    .back-to-top {
        width: 42px;
        height: 42px;
        bottom: 25px;
        right: 25px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .back-to-top {
        width: 40px;
        height: 40px;
        bottom: 20px;
        right: 20px;
        font-size: 0.9rem;
    }
}

@media (max-width: 380px) {
    .back-to-top {
        width: 38px;
        height: 38px;
        bottom: 15px;
        right: 15px;
    }
}
