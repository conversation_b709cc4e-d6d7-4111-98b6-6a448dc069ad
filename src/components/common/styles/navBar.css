@import "../../../data/styles.css";

.nav-container {
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 85%; /* Consistent with other containers */
}

.navbar {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 3svh;
    z-index: 999;
    width: 85svw; /* Consistent with container width */
    max-width: 450px; /* Slightly increased maximum width */
}

.nav-background {
    display: flex;
    width: 100%; /* Take full width of parent */
    height: 40px;
    background: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    border-radius: 40px;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px; /* Increased padding for better spacing */
}

.nav-list {
    display: flex;
    justify-content: space-between;
    list-style: none;
    align-items: center;
    margin: 0; /* Reset default margin */
    padding: 10px;
    width: 100%; /* Take full width of parent */
}

.divider {
}

.nav-item {
    font-weight: bold;
    font-size: 0.9rem; /* Increased font size */
    cursor: pointer;
    border: 40px;
    padding: 8px 12px; /* Adjusted padding */
}

.active {
    font-weight: bolder;
}

.nav-item a {
    text-decoration: none;
    color: var(--primary-color);
}

.bg-blur {
    background: rgba(197, 197, 197, 0.25);
    backdrop-filter: blur(10px);
}