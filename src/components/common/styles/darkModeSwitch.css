/* styles/darkModeSwitch.css */

.dark-mode-switch-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: 10px; /* Keep if needed */
}

/* Keep styling for the overall switch container minimal */
.mui-dark-mode-switch {
    vertical-align: middle;
}

.react-switch-bg{
    background-color: rgba(36,59,8,0);
    color: white;
    filter: blur(3px);
}

/* Style the handle itself - mainly for overflow or if needed later */
.react-switch-handle {
    /* The library uses inline styles for position, size, bg */
    /* We might need 'overflow: hidden' if icon is slightly too big */
    overflow: hidden;
}

/* --- This is the key part --- */
/* Target the icon *within* the handle structure */
.react-switch-handle .dark-mode-icons {
    position: absolute; /* Position relative to the intermediate div */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); /* The centering trick */

    /* Set desired color (might be overridden by inline styles, but try) */
    color: white;

    /* Optional: Add max size to prevent icon hitting edges */
    max-width: 85%;
    max-height: 85%;

    /* Remove any conflicting styles from previous attempts */
    display: inline-block; /* Reset display if previously changed */
    margin: 0; /* Reset margin */
}