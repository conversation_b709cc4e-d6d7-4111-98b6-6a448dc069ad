/* styles/darkModeSwitch.css */

.dark-mode-switch-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: 10px;
    transition: all var(--transition-fast);
}

.dark-mode-switch-wrapper:hover {
    transform: scale(1.05);
}

/* Keep styling for the overall switch container minimal */
.mui-dark-mode-switch {
    vertical-align: middle;
    transition: all var(--transition-fast);
}

.react-switch-bg{
    background-color: rgba(197, 197, 197, 0.2) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-fast);
}

/* Style the handle itself - mainly for overflow or if needed later */
.react-switch-handle {
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    transition: all var(--transition-fast) !important;
}

/* --- This is the key part --- */
/* Target the icon *within* the handle structure */
.react-switch-handle .dark-mode-icons {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--primary-color) !important;
    max-width: 80%;
    max-height: 80%;
    display: inline-block;
    margin: 0;
    transition: all var(--transition-fast);
}

/* Responsive Design */
@media (max-width: 768px) {
    .dark-mode-switch-wrapper {
        padding-left: 8px;
    }

    .mui-dark-mode-switch {
        transform: scale(0.9);
    }
}

@media (max-width: 480px) {
    .dark-mode-switch-wrapper {
        padding-left: 5px;
    }

    .mui-dark-mode-switch {
        transform: scale(0.85);
    }
}

@media (max-width: 380px) {
    .mui-dark-mode-switch {
        transform: scale(0.8);
    }
}