/* styles/darkModeSwitch.css */

.dark-mode-switch-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: 12px;
    transition: all var(--transition-fast);
    border-radius: 15px;
    padding: 4px;
}

.dark-mode-switch-wrapper:hover {
    transform: scale(1.08);
    background-color: var(--hover-overlay);
}

/* Keep styling for the overall switch container minimal */
.mui-dark-mode-switch {
    vertical-align: middle;
    transition: all var(--transition-fast);
    border-radius: 12px !important;
}

.react-switch-bg{
    backdrop-filter: blur(15px) !important;
    border: 1px solid var(--glass-border) !important;
    transition: all var(--transition-fast) !important;
    border-radius: 12px !important;
}

/* Style the handle itself - mainly for overflow or if needed later */
.react-switch-handle {
    overflow: hidden !important;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.15) !important,
        0 1px 3px rgba(0, 0, 0, 0.1) !important;
    transition: all var(--transition-fast) !important;
    border-radius: 50% !important;
    backdrop-filter: blur(10px) !important;
}

/* --- This is the key part --- */
/* Target the icon *within* the handle structure */
.react-switch-handle .dark-mode-icons {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--primary-color) !important;
    max-width: 70%;
    max-height: 70%;
    display: inline-block;
    margin: 0;
    transition: all var(--transition-fast);
    font-size: 0.8rem !important;
}

/* Dark mode specific icon colors */
.dark .react-switch-handle .dark-mode-icons {
    color: #e0e0e0 !important;
}

/* Icon animation on toggle */
.react-switch-handle .dark-mode-icons:hover {
    transform: translate(-50%, -50%) scale(1.1);
}

/* Dark mode styles */
.dark .dark-mode-switch-wrapper:hover {
    background-color: var(--dark-hover-overlay);
}

.dark .react-switch-bg {
    border: 1px solid var(--dark-glass-border) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dark-mode-switch-wrapper {
        padding-left: 8px;
        padding: 3px;
    }

    .mui-dark-mode-switch {
        transform: scale(0.95);
    }

    .react-switch-handle .dark-mode-icons {
        font-size: 0.75rem !important;
    }
}

@media (max-width: 480px) {
    .dark-mode-switch-wrapper {
        padding-left: 6px;
        padding: 2px;
    }

    .mui-dark-mode-switch {
        transform: scale(0.9);
    }

    .react-switch-handle .dark-mode-icons {
        font-size: 0.7rem !important;
    }
}

@media (max-width: 380px) {
    .dark-mode-switch-wrapper {
        padding: 2px;
    }

    .mui-dark-mode-switch {
        transform: scale(0.85);
    }

    .react-switch-handle .dark-mode-icons {
        font-size: 0.65rem !important;
    }
}