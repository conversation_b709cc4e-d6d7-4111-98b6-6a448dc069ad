import React, { useEffect, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import Switch from 'react-switch';
import './styles/darkModeSwitch.css'; // Make sure the path is correct
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMoon, faSun } from '@fortawesome/free-solid-svg-icons';

function DarkModeSwitch() {
	const [isDarkMode, setDarkMode] = useState(() => {
		const storedTheme = localStorage.getItem('darkTheme');
		return storedTheme === 'true';
	});

	const toggleDarkMode = () => {
		setDarkMode(!isDarkMode);
	};

	const isSystemDarkMode = useMediaQuery({
		query: '(prefers-color-scheme: dark)',
	});

	useEffect(() => {
		const newDarkMode = typeof localStorage.getItem('darkTheme') == 'string' ? localStorage.getItem('darkTheme') === 'true' : isSystemDarkMode;
		setDarkMode(newDarkMode);
	}, [isSystemDarkMode]);

	useEffect(() => {
		localStorage.setItem('darkTheme', isDarkMode.toString()); // Store boolean as string
		if (isDarkMode) {
			document.body.classList.add('dark');
		} else {
			document.body.classList.remove('dark');
		}
	}, [isDarkMode]);

	return (
		<React.Fragment>
			<div className="dark-mode-switch-wrapper">
				<Switch onColor="#666362" offColor="#C0C0C0"  className="mui-dark-mode-switch" checked={isDarkMode}
				        onChange={toggleDarkMode} offHandleColor="#808080" onHandleColor="#3A3B3C" checkedIcon={false}
				        uncheckedIcon={false} uncheckedHandleIcon={<FontAwesomeIcon className='dark-mode-icons' size={'lg'}  icon={faSun}/>}
				        checkedHandleIcon={<FontAwesomeIcon className='dark-mode-icons' size={'lg'} icon={faMoon}/>}></Switch>
			</div>
		</React.Fragment>
	);

}

export default DarkModeSwitch;