import React, { useState, useEffect, useCallback, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowUp } from '@fortawesome/free-solid-svg-icons';
import './styles/scrollProgress.css';

const ScrollProgress = () => {
    const [scrollProgress, setScrollProgress] = useState(0);
    const [showButton, setShowButton] = useState(false);
    const [isScrolling, setIsScrolling] = useState(false);
    const rafRef = useRef(null);
    const scrollTimeoutRef = useRef(null);

    const calculateProgress = useCallback(() => {
        // Account for fixed navbar height
        const navbarHeight = 90; // Matches the body padding-top

        // Get the content wrapper element for more accurate scroll calculation
        const contentWrapper = document.querySelector('.content-wrapper');
        const scrollContainer = contentWrapper || document.documentElement;

        // Calculate scroll values accounting for navbar
        const totalScrollHeight = scrollContainer.scrollHeight - window.innerHeight;
        const currentScroll = window.scrollY;

        // Ensure we don't go below 0 or above 100
        const progress = Math.max(0, Math.min((currentScroll / totalScrollHeight) * 100, 100));

        // Handle edge case where totalScrollHeight might be 0
        const finalProgress = totalScrollHeight > 0 ? progress : 0;

        return finalProgress;
    }, []);

    const handleScroll = useCallback(() => {
        const progress = calculateProgress();
        const currentScroll = window.scrollY;

        setScrollProgress(progress);
        setShowButton(currentScroll > 150); // Reduced threshold for better UX
        setIsScrolling(true);

        // Clear existing timeout
        if (scrollTimeoutRef.current) {
            clearTimeout(scrollTimeoutRef.current);
        }

        // Set scrolling to false after scroll ends
        scrollTimeoutRef.current = setTimeout(() => {
            setIsScrolling(false);
        }, 150);
    }, [calculateProgress]);

    useEffect(() => {
        // Optimized scroll handler with RAF throttling
        const throttledHandleScroll = () => {
            if (rafRef.current) {
                cancelAnimationFrame(rafRef.current);
            }

            rafRef.current = requestAnimationFrame(handleScroll);
        };

        // Initial calculation
        handleScroll();

        // Add scroll listener
        window.addEventListener('scroll', throttledHandleScroll, { passive: true });

        // Handle resize events that might affect scroll calculation
        const handleResize = () => {
            setTimeout(handleScroll, 100); // Delay to ensure layout is complete
        };
        window.addEventListener('resize', handleResize, { passive: true });

        return () => {
            window.removeEventListener('scroll', throttledHandleScroll);
            window.removeEventListener('resize', handleResize);

            if (rafRef.current) {
                cancelAnimationFrame(rafRef.current);
            }
            if (scrollTimeoutRef.current) {
                clearTimeout(scrollTimeoutRef.current);
            }
        };
    }, [handleScroll]);

    const scrollToTop = useCallback(() => {
        // Smooth scroll to top with better performance
        const scrollStep = -window.scrollY / (500 / 15); // 500ms duration

        const scrollAnimation = () => {
            if (window.scrollY !== 0) {
                window.scrollBy(0, scrollStep);
                requestAnimationFrame(scrollAnimation);
            }
        };

        // Fallback to native smooth scroll for better browser support
        if ('scrollBehavior' in document.documentElement.style) {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        } else {
            requestAnimationFrame(scrollAnimation);
        }
    }, []);

    return (
        <>
            <div className={`scroll-progress-container ${isScrolling ? 'scrolling' : ''}`}>
                <div
                    className={`scroll-progress-bar ${scrollProgress > 0 ? 'active' : ''}`}
                    style={{
                        width: `${scrollProgress}%`,
                        opacity: scrollProgress > 0 ? 1 : 0
                    }}
                ></div>
            </div>
            {showButton && (
                <button
                    className="back-to-top hover-scale smooth-transition"
                    onClick={scrollToTop}
                    aria-label="Back to top"
                    style={{
                        opacity: showButton ? 1 : 0,
                        transform: showButton ? 'scale(1)' : 'scale(0.8)'
                    }}
                >
                    <FontAwesomeIcon icon={faArrowUp} />
                </button>
            )}
        </>
    );
};

export default ScrollProgress;
