import {useEffect} from 'react';
import {Route, Routes} from 'react-router-dom';
import ReactGA from 'react-ga4';

import Homepage from './pages/homepage';
import Notfound from './pages/404';
import ScrollProgress from './components/common/scrollProgress';

import {TRACKING_ID} from './data/tracking';
import './App.css';
import './components/common/styles/animations.css';

function App() {
	useEffect(() => {
		if (TRACKING_ID !== '') {
			ReactGA.initialize(TRACKING_ID);
		}
	}, []);

	return (
		<div className="App">
			<ScrollProgress />
			<Routes>
				<Route path="/" element={<Homepage/>}/>
				<Route path="*" element={<Notfound/>}/>
			</Routes>
		</div>
	);
}

export default App;
