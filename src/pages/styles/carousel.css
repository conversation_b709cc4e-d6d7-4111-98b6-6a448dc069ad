/* Main container for the carousel */
.outer-carousel-container {
    position: relative;
    width: 100%;
    max-width: 100%;
    padding: 0;
    box-sizing: border-box;
    margin: 0 auto;
}

/* Inner container */
.carousel-container {
    position: relative;
    width: 100%;
    overflow: visible;
}

/* The carousel itself */
.skills-carousel {
    position: relative;
    width: 100%;
    margin: 0 auto;
}

/* Navigation buttons wrapper */
.nav-buttons-wrapper {
    position: absolute !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 1000 !important;
    width: 50px !important;
    height: 50px !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Position the left button */
.nav-buttons-wrapper:first-of-type {
    left: -50px !important;
}

/* Position the right button */
.nav-buttons-wrapper:last-of-type {
    right: -50px !important;
}

/* Style the navigation buttons */
.carousel-nav-btn {
    background: var(--glass-hover) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 50% !important;
    width: 45px !important;
    height: 45px !important;
    border: 1px solid var(--glass-border) !important;
    box-shadow: none !important;
    transition: all 0.2s ease !important;
    color: var(--icon-default) !important;
    font-size: 1.5rem !important;
}

.carousel-nav-btn:hover {
    background: var(--glass-active) !important;
    border-color: var(--glass-border-hover) !important;
    color: var(--icon-hover) !important;
    transform: scale(1.05) !important;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.3) !important;
}

/* Carousel slide */
.carousel-slide {
    padding: 20px 40px;
    box-sizing: border-box;
    min-height: 350px; /* Increased height */
    height: auto; /* Allow height to adjust to content */
    display: flex;
    align-items: center;
    justify-content: center;
    width: 95%;
    margin: 0 auto;
}

/* Indicator container */
.carousel-indicators {
    margin-top: 20px !important;
    margin-bottom: 10px !important;
    display: flex !important;
    justify-content: center !important;
}

/* Indicator buttons */
.MuiButton-root {
    min-width: 8px !important;
    width: 8px !important;
    height: 10px !important;
    padding: 5px !important;
    margin: 5px 5px !important;
    border-radius: 50% !important;
    background: var(--glass-base) !important;
    border: 1px solid var(--glass-border) !important;
    transition: all 0.2s ease !important;
}

.MuiButton-root:hover {
    background: var(--glass-hover) !important;
    border-color: var(--glass-border-hover) !important;
    transform: scale(1.1) !important;
}

.MuiButton-root.active {
    background: var(--glass-active) !important;
    border-color: var(--glass-border-hover) !important;
    transform: scale(1.1) !important;
}

/* Make sure the carousel paper has no background */
.MuiPaper-root {
    background-color: transparent !important;
    box-shadow: none !important;
}

/* Ensure the projects list container is properly sized */
.projects-list {
    position: relative;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: visible;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Media query for smaller screens */
@media (max-width: 768px) {
    .outer-carousel-container {
        width: 100%;
        padding: 0;
    }

    .projects-list {
        padding: 0;
    }

    .nav-buttons-wrapper:first-of-type {
        left: -25px !important;
    }

    .nav-buttons-wrapper:last-of-type {
        right: -25px !important;
    }

    .carousel-nav-btn {
        width: 35px !important;
        height: 35px !important;
        font-size: 1.1rem !important;
        background: var(--glass-active) !important;
    }

    .carousel-slide {
        min-height: 400px; /* Increased height for smaller screens */
        height: auto;
        padding: 20px 30px;
    }
}

/* Media query for very small screens (mobile phones) */
@media (max-width: 480px) {
    .nav-buttons-wrapper:first-of-type {
        left: -15px !important;
    }

    .nav-buttons-wrapper:last-of-type {
        right: -15px !important;
    }

    .carousel-nav-btn {
        width: 30px !important;
        height: 30px !important;
        font-size: 1rem !important;
    }

    .carousel-slide {
        padding: 15px 20px;
    }
}

/* Dark mode styles */
.dark .carousel-nav-btn {
    background: var(--dark-glass-hover) !important;
    border-color: var(--dark-glass-border) !important;
    color: var(--dark-icon-default) !important;
}

.dark .carousel-nav-btn:hover {
    background: var(--dark-glass-active) !important;
    border-color: var(--dark-glass-border-hover) !important;
    color: var(--dark-icon-hover) !important;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.2) !important;
}

.dark .MuiButton-root {
    background: var(--dark-glass-base) !important;
    border-color: var(--dark-glass-border) !important;
}

.dark .MuiButton-root:hover {
    background: var(--dark-glass-hover) !important;
    border-color: var(--dark-glass-border-hover) !important;
}

.dark .MuiButton-root.active {
    background: var(--dark-glass-active) !important;
    border-color: var(--dark-glass-border-hover) !important;
}
