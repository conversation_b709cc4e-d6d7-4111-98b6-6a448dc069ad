.title {
    color: #252525;
}

.subtitle {
    color: rgb(47, 47, 47);
}

.homepage-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 6% 8%;
    width: 85%;
    max-width: 1200px;
    margin: 0 auto;
    color: rgba(110, 110, 110, 0.46);
    backdrop-filter: blur(20px);
    background: var(--glass-base);
    box-shadow: 0 4px 8px rgba(54, 54, 54, 0.3);
    border-radius: 30px;
    border: 1px solid var(--glass-border);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    position: relative;
    overflow: visible;
    box-sizing: border-box;
}

.homepage-container:hover {
    transform: translateY(-5px);
    background: var(--glass-hover);
    border-color: var(--glass-border-hover);
    box-shadow:
        0 8px 16px rgba(54, 54, 54, 0.4),
        0 0 25px rgba(255, 255, 255, 0.2);
}

/* Tablet styles */
@media (max-width: 1024px) {
    .homepage-container {
        width: 90%;
        max-width: 900px;
        padding: 5% 6%;
        margin: 0 auto;
    }
}

/* Mobile landscape and small tablets */
@media (max-width: 768px) {
    .homepage-container {
        width: 92%;
        max-width: 700px;
        padding: 5% 6%;
        margin: 0 auto;
        overflow: visible !important;
    }
}

/* Mobile portrait */
@media (max-width: 480px) {
    .homepage-container {
        width: 95%;
        max-width: 400px;
        padding: 4% 5%;
        margin: 0 auto;
        border-radius: 20px;
    }
}

/* Very small screens */
@media (max-width: 380px) {
    .homepage-container {
        width: 98%;
        padding: 4% 4%;
        margin: 0 auto;
        border-radius: 15px;
    }
}

.homepage-socials {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 15px;
}

.homepage-social-icon {
    height: 25px;
    margin: 0 5px;
    transition: transform var(--transition-fast), color var(--transition-fast);
    font-size: 1.5rem;
}

.homepage-social-icon:hover {
    transform: scale(1.2);
    color: var(--accent-color);
}

.bg-blur {
    backdrop-filter: blur(10px);
}

.homepage-first-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.homepage-first-area-left-side {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 100%;
}

@media (max-width: 480px) {
    .homepage-title h2 {
        font-size: 1.8rem;
        text-align: center;
        margin-bottom: 0.5rem;
    }

    .homepage-subtitle h4 {
        font-size: 1.1rem;
        text-align: center;
        line-height: 1.4;
    }

    .homepage-social-icon {
        font-size: 1.3rem;
    }
}
