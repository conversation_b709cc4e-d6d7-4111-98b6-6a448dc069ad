import {motion} from 'motion/react';
import INFO from '../../data/user.js';
import {FontAwesomeIcon} from '@fortawesome/react-fontawesome';
import {faGithub} from '@fortawesome/free-brands-svg-icons';
import {faMailBulk} from '@fortawesome/free-solid-svg-icons';
import React from 'react';
import '../styles/homepage.css'

function Home() {
	return (
		<React.Fragment>
			<section id="home" className="scroll-child" style={{ scrollSnapAlign: 'start', height: '100vh' }}>
				<div className="white-space"></div>

				<motion.div
					initial={{opacity: 0}}
					whileInView={{opacity: 1}}
					transition={{duration: 0.75}}
					className="homepage-container">
					<div className="homepage-first-area">
						<div className="homepage-first-area-left-side">
							<div className="title homepage-title">
								<h2>
									{INFO.homepage.title}
								</h2>
							</div>

							<div className="subtitle homepage-subtitle">
								<h4>
									{INFO.homepage.description}
								</h4>
							</div>
						</div>
						<div className="homepage-socials" style={{display: 'flex', justifyContent: 'center', gap: '20px', marginTop: '15px'}}>
							<a
								href={INFO.socials.github}
								target="_blank"
								rel="noreferrer"
							>
								<FontAwesomeIcon
									icon={faGithub}
									className="homepage-social-icon social-icon"
								/>
							</a>
							<a
								href={`mailto:${INFO.main.email}`}
								target="_blank"
								rel="noreferrer"
							>
								<FontAwesomeIcon
									icon={faMailBulk}
									className="homepage-social-icon social-icon"
								/>
							</a>

						</div>

					</div>


				</motion.div>
			</section>
		</React.Fragment>
	);
}

export default Home;