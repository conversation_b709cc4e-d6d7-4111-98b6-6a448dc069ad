@import "../../../data/styles.css";

.social,
.email-wrapper {
    display: flex;
    padding-bottom: 15px;
}

.social a,
.email-wrapper a {
    text-decoration: none;
}

.social-icon {
    font-size: 1.2rem;
    color: var(--accent-color);
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all var(--transition-fast);
}

.email-wrapper:hover .social-icon {
    color: var(--link-color);
    transform: scale(1.1);
}

.dark .social-icon {
    color: var(--accent-color);
}

.dark .email-wrapper:hover .social-icon {
    color: var(--link-color);
}

.social-text {
    padding: 0;
    margin: 0;
    font-weight: 600;
    font-size: 1rem;
    color: var(--primary-color);
    transition: all var(--transition-fast);
}

.email-wrapper:hover .social-text {
    color: var(--accent-color);
}

.dark .social-text {
    color: #e0e0e0;
}

.dark .email-wrapper:hover .social-text {
    color: var(--accent-color);
}

.email::before {
    content: "";
    display: block;
    padding-top: 20px;
    border-top: 3px solid #f4f4f5;
    margin-top: 20px;
}

.socials-container {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
}

.contact-socials {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.socials {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.email-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 0;
    margin: 0;
}

.email-wrapper a {
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    padding: 15px 25px;
    background: rgba(197, 197, 197, 0.2);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
    gap: 15px;
    min-width: 280px;
}

.email-wrapper a:hover {
    background: rgba(197, 197, 197, 0.3);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Dark mode styles */
.dark .email-wrapper a {
    background: rgba(159, 159, 159, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .email-wrapper a:hover {
    background: rgba(159, 159, 159, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .email-wrapper a {
        min-width: 250px;
        padding: 12px 20px;
        gap: 12px;
    }

    .social-icon {
        font-size: 1.1rem;
    }

    .social-text {
        font-size: 0.95rem;
    }
}

@media (max-width: 480px) {
    .email-wrapper a {
        min-width: 220px;
        padding: 10px 15px;
        gap: 10px;
        border-radius: 20px;
    }

    .social-icon {
        font-size: 1rem;
    }

    .social-text {
        font-size: 0.9rem;
    }

    .socials-container {
        margin-top: 15px;
    }
}

@media (max-width: 380px) {
    .email-wrapper a {
        min-width: 200px;
        padding: 8px 12px;
        gap: 8px;
    }

    .social-icon {
        font-size: 0.95rem;
    }

    .social-text {
        font-size: 0.85rem;
    }
}
