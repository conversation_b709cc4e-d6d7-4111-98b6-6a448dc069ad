import {motion} from 'motion/react';
import INFO from '../../data/user.js';
import React from 'react';
import Carousel from 'react-material-ui-carousel';
import Project from '../../components/projects/project.jsx';
import '../styles/carousel.css';

function Projects() {
	return (
		<React.Fragment>
			<section id="projects" className="scroll-child">
				<div className="white-space"></div>
				<motion.div initial={{opacity: 0}}
				            whileInView={{opacity: 1}}
				            transition={{duration: 0.75}}
				            className="homepage-container bg-blur">
					<div className="title projects-title">
						<h2>Skills</h2>
					</div>

					<div className="projects-list" role="region" aria-label="Skills carousel">
						<Carousel
							navButtonsAlwaysVisible={true}
							navButtonsProps={{
								className: "carousel-nav-btn",
								style: {
									backgroundColor: "rgba(197, 197, 197, 0.7)",
									color: "#27272a",
									padding: 0
								}
							}}
							navButtonsWrapperProps={{
								className: "nav-buttons-wrapper"
							}}
							indicatorContainerProps={{
								className: "carousel-indicators"
							}}
							className="skills-carousel"
							animation="slide"
							autoPlay={false}
							interval={6000}
							fullHeightHover={false}
							style={{ width: '100%' }}
						>
							{INFO.projects.map((project, index) => (
								<div className="carousel-slide" key={index}>
									<Project
										logo={project.logo}
										title={project.title}
										description={project.description}
									/>
								</div>
							))}
						</Carousel>
					</div>
				</motion.div>
			</section>
		</React.Fragment>
	);
}

export default Projects;
