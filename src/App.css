html {
    scroll-snap-type: y mandatory;
    overflow-y: scroll;
    overflow-x: hidden;
    color: #252525;
    scroll-behavior: smooth;
    background-size: contain;
    background: linear-gradient(-45deg, #9cc4bd, #9dc095, #9eb4bd, #b6a884);
    background-size: 550svw 550svh;
    animation: gradient  30s ease-in-out infinite alternate both;
    width: 100%;
    max-width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

body {
    margin: 0;
    padding: 0;
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    flex-direction: column;
    height: 100%;
}

@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}


@keyframes pulse {
}

.scroll-child {
    display: flex;
    flex-direction: column;
    width: 85%;
    max-width: 1200px;
    margin-left: auto; /* Center the block */
    margin-right: auto; /* Center the block */
    justify-content: center;
    align-items: center;
    align-content: center;
    scroll-snap-align: start;
    height: 100svh;
    min-height: 100vh;
    box-sizing: border-box;
    padding: 0;
}

.contact-with-footer {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    height: 100svh;
    padding: 0;
    width: 100%;
    margin: 0 auto;
}

.contact-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
    width: 100%;
}

.footer-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    padding-bottom: 30px;
    margin-top: 20px;
}

.white-space {
}

/* Loading spinner */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    width: 100%;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.content-wrapper {
    display: flex;
    justify-content: space-around;
    flex-direction: column;
    width: 100%;
    max-width: 100%;
    margin: 0 auto 0;
    padding: 0;
    color: rgba(36,59,8,0);
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    scroll-snap-type: y mandatory;
    scroll-behavior: smooth;
}

.title {
    color: #252525;
}

.subtitle {
    color: rgb(47, 47, 47);
}


.App {
    background-size: contain;
    background: linear-gradient(-45deg, #526c67, #5b6751, #4d5a64, #645f49) repeat;
    background-size: 550svw 550svh;
    animation: gradient 30s ease-in-out infinite alternate both;
    height: 100vh;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    scroll-snap-type: y mandatory;
    padding: 0;
    margin: 0;
}
/* Dark Mode (Applied via class) */

.dark .homepage-container, .dark .nav-background {
    background-color: rgba(159, 159, 159, 0.24);
}

.dark .link-text {
    color: #e0e0e0;
}

.dark .title {
    color: #e0e0e0;
}

.dark .subtitle {
    color: #bdbdbd;
}

/* Toggle Switch Style */
.dark-mode-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #333;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    z-index: 1000;
}

@media (max-width: 1270px) {
    .content-wrapper {
        width: 100%;
        max-width: 100%;
        padding: 0;
    }
}

@media (max-width: 1024px) {
    .content-wrapper {
        width: 100%;
        max-width: 100%;
        padding: 0;
    }
}

@media (max-width: 1270px) {
    .content-wrapper {
        width: 100%;
        max-width: 100%;
        padding: 0;
        scroll-snap-type: y mandatory;
        height: 100vh;
    }
}
        .scroll-child {
            width: 85%;
            max-width: 1200px;
            padding: 0;
            scroll-snap-align: start;
            height: 100svh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
}

@media (max-width: 480px) {
    .homepage-container {
        width: 85%;
        padding: 5% 6%;
    }
    
    .scroll-child {
        padding: 0;
        width: 85%;
    }
    
    .content-wrapper {
        width: 100%;
        padding: 0;
    }
}