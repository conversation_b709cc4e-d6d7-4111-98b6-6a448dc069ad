html {
    overflow-y: hidden; /* Let content-wrapper handle scrolling */
    overflow-x: hidden;
    color: #252525;
    background: linear-gradient(
        135deg,
        #f8fafc 0%,
        #e2e8f0 25%,
        #cbd5e1 50%,
        #94a3b8 75%,
        #64748b 100%
    );
    background-size: 400% 400%;
    animation: professionalGradient 45s ease-in-out infinite;
    width: 100%;
    max-width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

body {
    margin: 0;
    padding: 0;
    width: 100%;
    max-width: 100%;
    overflow: hidden; /* Prevent body scrolling */
    flex-direction: column;
    height: 100vh;
    position: fixed; /* Prevent iOS bounce scrolling */
}

/* Professional gradient animations */
@keyframes professionalGradient {
    0% {
        background-position: 0% 50%;
    }
    25% {
        background-position: 100% 25%;
    }
    50% {
        background-position: 50% 100%;
    }
    75% {
        background-position: 25% 0%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes subtleFlow {
    0% {
        background-position: 0% 0%;
    }
    33% {
        background-position: 100% 50%;
    }
    66% {
        background-position: 0% 100%;
    }
    100% {
        background-position: 0% 0%;
    }
}

/* Dark mode gradient animations */
@keyframes professionalGradientDark {
    0% {
        background-position: 0% 50%;
    }
    20% {
        background-position: 80% 20%;
    }
    40% {
        background-position: 20% 80%;
    }
    60% {
        background-position: 90% 40%;
    }
    80% {
        background-position: 10% 90%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes subtleFlowDark {
    0% {
        background-position: 0% 0%;
    }
    30% {
        background-position: 70% 30%;
    }
    60% {
        background-position: 30% 70%;
    }
    100% {
        background-position: 0% 0%;
    }
}

/* Performance optimizations and accessibility */
@media (prefers-reduced-motion: reduce) {
    html,
    .App,
    .dark html,
    .dark .App {
        animation: none !important;
        background-position: 0% 50% !important;
    }
}

/* GPU acceleration for smooth animations */
html,
.App {
    will-change: background-position;
    transform: translateZ(0);
    backface-visibility: hidden;
}

@keyframes pulse {
    /* Kept for compatibility */
}

.scroll-child {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 100vw;
    margin: 0 auto;
    justify-content: center;
    align-items: center;
    align-content: center;
    scroll-snap-align: start;
    height: 100vh;
    min-height: 100vh;
    box-sizing: border-box;
    padding: 90px 2% 0 2%; /* Top padding for navbar */
    overflow-x: hidden;
    scroll-snap-stop: always; /* Force snap on each section */
}

.contact-with-footer {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    height: 100svh;
    padding: 0;
    width: 100%;
    margin: 0 auto;
}

.contact-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
    width: 100%;
}

.footer-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 30px;
    margin-top: auto;
    min-height: 120px;
    position: relative;
}

.white-space {
}

/* Loading spinner */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    width: 100%;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.content-wrapper {
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
    color: rgba(36,59,8,0);
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    scroll-snap-type: y mandatory;
    scroll-behavior: smooth;
    scroll-padding-top: 0; /* Handled by section padding */
    /* Improve scroll performance */
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.content-wrapper::-webkit-scrollbar {
    display: none;
}

.title {
    color: #252525;
}

.subtitle {
    color: rgb(47, 47, 47);
}


.App {
    background: linear-gradient(
        -45deg,
        rgba(248, 250, 252, 0.8) 0%,
        rgba(226, 232, 240, 0.6) 25%,
        rgba(203, 213, 225, 0.4) 50%,
        rgba(148, 163, 184, 0.6) 75%,
        rgba(100, 116, 139, 0.8) 100%
    );
    background-size: 300% 300%;
    animation: subtleFlow 60s ease-in-out infinite;
    height: 100vh;
    width: 100%;
    overflow: hidden; /* No scrolling on App level */
    padding: 0;
    margin: 0;
    position: relative;
}
/* Dark Mode (Applied via class) */

/* Dark mode background overrides */
.dark html {
    background: linear-gradient(
        135deg,
        #0f172a 0%,
        #1e293b 25%,
        #334155 50%,
        #475569 75%,
        #64748b 100%
    );
    background-size: 400% 400%;
    animation: professionalGradientDark 50s ease-in-out infinite;
}

.dark .App {
    background: linear-gradient(
        -45deg,
        rgba(15, 23, 42, 0.9) 0%,
        rgba(30, 41, 59, 0.7) 25%,
        rgba(51, 65, 85, 0.5) 50%,
        rgba(71, 85, 105, 0.7) 75%,
        rgba(100, 116, 139, 0.9) 100%
    );
    background-size: 300% 300%;
    animation: subtleFlowDark 65s ease-in-out infinite;
}

.dark .homepage-container {
    background: var(--dark-glass-base);
    border-color: var(--dark-glass-border);
}

.dark .homepage-container:hover {
    background: var(--dark-glass-hover);
    border-color: var(--dark-glass-border-hover);
    box-shadow:
        0 8px 16px rgba(54, 54, 54, 0.4),
        0 0 20px rgba(255, 255, 255, 0.1);
}

.dark .link-text {
    color: #e0e0e0;
}

.dark .title {
    color: #e0e0e0;
}

.dark .subtitle {
    color: #bdbdbd;
}

/* Toggle Switch Style - Legacy (now handled by navbar) */
.dark-mode-toggle {
    display: none; /* Hidden as we now use the navbar toggle */
}

@media (max-width: 1270px) {
    .content-wrapper {
        width: 100%;
        max-width: 100%;
        padding: 0;
    }
}

@media (max-width: 1024px) {
    .content-wrapper {
        width: 100%;
        max-width: 100%;
        padding: 0;
    }
}

/* Large screens and tablets */
@media (max-width: 1270px) {
    .content-wrapper {
        width: 100%;
        max-width: 100%;
        padding: 0;
        scroll-snap-type: y mandatory;
        height: 100vh;
    }

    .scroll-child {
        width: 100%;
        max-width: 100vw;
        padding: 80px 3% 0 3%; /* Adjusted for responsive navbar */
        scroll-snap-align: start;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* Tablets */
@media (max-width: 1024px) {
    .scroll-child {
        padding: 80px 4% 0 4%;
    }
}

/* Mobile landscape and small tablets */
@media (max-width: 768px) {
    .scroll-child {
        padding: 70px 4% 0 4%;
        width: 100%;
    }

    .content-wrapper {
        width: 100%;
        padding: 0;
    }
}

/* Mobile portrait */
@media (max-width: 480px) {
    .scroll-child {
        padding: 60px 2.5% 0 2.5%;
        width: 100%;
    }

    .content-wrapper {
        width: 100%;
        padding: 0;
    }
}

/* Very small screens */
@media (max-width: 380px) {
    .scroll-child {
        padding: 55px 1% 0 1%;
        width: 100%;
    }
}

/* Mobile scroll-snap optimizations */
@media (max-width: 768px) {
    .content-wrapper {
        scroll-snap-type: y mandatory;
        /* Improve touch scrolling on mobile */
        -webkit-overflow-scrolling: touch;
        overscroll-behavior-y: contain;
    }

    .scroll-child {
        scroll-snap-stop: always;
    }

    /* Optimize background animations for mobile */
    html,
    .App {
        animation-duration: 60s, 80s;
        background-size: 250% 250%;
    }

    .dark html,
    .dark .App {
        animation-duration: 70s, 90s;
        background-size: 250% 250%;
    }
}

@media (max-width: 480px) {
    .content-wrapper {
        /* More aggressive snap on small screens */
        scroll-snap-type: y mandatory;
    }

    /* Further optimize for small screens */
    html,
    .App {
        animation-duration: 80s, 100s;
        background-size: 200% 200%;
    }

    .dark html,
    .dark .App {
        animation-duration: 90s, 110s;
        background-size: 200% 200%;
    }
}

/* Very small screens - minimal animation */
@media (max-width: 380px) {
    html,
    .App,
    .dark html,
    .dark .App {
        animation-duration: 120s;
        background-size: 150% 150%;
    }
}