html {
    overflow-y: hidden; /* Let content-wrapper handle scrolling */
    overflow-x: hidden;
    color: #252525;
    background-size: contain;
    background: linear-gradient(-45deg, #9cc4bd, #9dc095, #9eb4bd, #b6a884);
    background-size: 550svw 550svh;
    animation: gradient  30s ease-in-out infinite alternate both;
    width: 100%;
    max-width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

body {
    margin: 0;
    padding: 0;
    width: 100%;
    max-width: 100%;
    overflow: hidden; /* Prevent body scrolling */
    flex-direction: column;
    height: 100vh;
    position: fixed; /* Prevent iOS bounce scrolling */
}

@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}


@keyframes pulse {
}

.scroll-child {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 100vw;
    margin: 0 auto;
    justify-content: center;
    align-items: center;
    align-content: center;
    scroll-snap-align: start;
    height: 100vh;
    min-height: 100vh;
    box-sizing: border-box;
    padding: 90px 2% 0 2%; /* Top padding for navbar */
    overflow-x: hidden;
    scroll-snap-stop: always; /* Force snap on each section */
}

.contact-with-footer {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    height: 100svh;
    padding: 0;
    width: 100%;
    margin: 0 auto;
}

.contact-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
    width: 100%;
}

.footer-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    padding-bottom: 30px;
    margin-top: 20px;
}

.white-space {
}

/* Loading spinner */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    width: 100%;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.content-wrapper {
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
    color: rgba(36,59,8,0);
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    scroll-snap-type: y mandatory;
    scroll-behavior: smooth;
    scroll-padding-top: 0; /* Handled by section padding */
    /* Improve scroll performance */
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.content-wrapper::-webkit-scrollbar {
    display: none;
}

.title {
    color: #252525;
}

.subtitle {
    color: rgb(47, 47, 47);
}


.App {
    background-size: contain;
    background: linear-gradient(-45deg, #526c67, #5b6751, #4d5a64, #645f49) repeat;
    background-size: 550svw 550svh;
    animation: gradient 30s ease-in-out infinite alternate both;
    height: 100vh;
    width: 100%;
    overflow: hidden; /* No scrolling on App level */
    padding: 0;
    margin: 0;
    position: relative;
}
/* Dark Mode (Applied via class) */

.dark .homepage-container {
    background: var(--dark-glass-base);
    border-color: var(--dark-glass-border);
}

.dark .homepage-container:hover {
    background: var(--dark-glass-hover);
    border-color: var(--dark-glass-border-hover);
}

.dark .link-text {
    color: #e0e0e0;
}

.dark .title {
    color: #e0e0e0;
}

.dark .subtitle {
    color: #bdbdbd;
}

/* Toggle Switch Style - Legacy (now handled by navbar) */
.dark-mode-toggle {
    display: none; /* Hidden as we now use the navbar toggle */
}

@media (max-width: 1270px) {
    .content-wrapper {
        width: 100%;
        max-width: 100%;
        padding: 0;
    }
}

@media (max-width: 1024px) {
    .content-wrapper {
        width: 100%;
        max-width: 100%;
        padding: 0;
    }
}

/* Large screens and tablets */
@media (max-width: 1270px) {
    .content-wrapper {
        width: 100%;
        max-width: 100%;
        padding: 0;
        scroll-snap-type: y mandatory;
        height: 100vh;
    }

    .scroll-child {
        width: 100%;
        max-width: 100vw;
        padding: 80px 3% 0 3%; /* Adjusted for responsive navbar */
        scroll-snap-align: start;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* Tablets */
@media (max-width: 1024px) {
    .scroll-child {
        padding: 80px 4% 0 4%;
    }
}

/* Mobile landscape and small tablets */
@media (max-width: 768px) {
    .scroll-child {
        padding: 70px 4% 0 4%;
        width: 100%;
    }

    .content-wrapper {
        width: 100%;
        padding: 0;
    }
}

/* Mobile portrait */
@media (max-width: 480px) {
    .scroll-child {
        padding: 60px 2.5% 0 2.5%;
        width: 100%;
    }

    .content-wrapper {
        width: 100%;
        padding: 0;
    }
}

/* Very small screens */
@media (max-width: 380px) {
    .scroll-child {
        padding: 55px 1% 0 1%;
        width: 100%;
    }
}

/* Mobile scroll-snap optimizations */
@media (max-width: 768px) {
    .content-wrapper {
        scroll-snap-type: y mandatory;
        /* Improve touch scrolling on mobile */
        -webkit-overflow-scrolling: touch;
        overscroll-behavior-y: contain;
    }

    .scroll-child {
        scroll-snap-stop: always;
    }
}

@media (max-width: 480px) {
    .content-wrapper {
        /* More aggressive snap on small screens */
        scroll-snap-type: y mandatory;
    }
}